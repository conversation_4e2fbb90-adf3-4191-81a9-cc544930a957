'use client';

import { useTranslations } from 'next-intl';
import { useState } from 'react';

// 导入多语言组件
import {
  CulturalCard,
  CulturalCardHeader,
  CulturalCardTitle,
  CulturalCardContent,
} from '@/components/ui/cultural-card';
import {
  CulturalText,
  CulturalSymbol,
  LuckySymbol,
  SpiritualSymbol,
} from '@/components/ui/cultural-symbols';
import { LanguageSwitcher } from '@/components/ui/language-switcher';
import {
  MobileMultilingualWrapper,
  MobileOptimizedButton,
  MobileOptimizedInput,
  MobileOptimizedCard,
} from '@/components/ui/mobile-multilingual-wrapper';
import { RTLButton, RTLNavButton } from '@/components/ui/rtl-button';

/**
 * 多语言功能演示组件
 * 展示所有多语言功能的集成使用
 */
export function MultilingualDemo() {
  const _t = useTranslations('homepage');
  const [inputValue, setInputValue] = useState('');

  return (
    <MobileMultilingualWrapper className='space-y-6 p-4'>
      {/* 语言切换器演示 */}
      <section className='space-y-4'>
        <h2 className='text-xl font-semibold'>语言切换器演示</h2>
        <div className='flex flex-wrap gap-4'>
          <LanguageSwitcher variant='default' />
          <LanguageSwitcher variant='compact' />
          <LanguageSwitcher variant='mobile' className='w-full sm:w-auto' />
        </div>
      </section>

      {/* 文化敏感设计演示 */}
      <section className='space-y-4'>
        <h2 className='text-xl font-semibold'>文化敏感设计演示</h2>
        <div className='grid grid-cols-1 gap-4 md:grid-cols-2'>
          <CulturalCard
            variant='mystical'
            showSymbol
            symbolType='spiritual'
            symbolPosition='top-right'
          >
            <CulturalCardHeader withSymbol symbolType='spiritual'>
              <CulturalCardTitle level={3}>
                <CulturalText>神秘学测试</CulturalText>
              </CulturalCardTitle>
            </CulturalCardHeader>
            <CulturalCardContent>
              <CulturalText>
                探索您的内在智慧，通过专业的AI分析获得深刻的洞察。
                <SpiritualSymbol className='ml-2' />
              </CulturalText>
            </CulturalCardContent>
          </CulturalCard>

          <CulturalCard
            variant='lucky'
            showSymbol
            symbolType='lucky'
            symbolPosition='top-left'
          >
            <CulturalCardHeader withSymbol symbolType='lucky'>
              <CulturalCardTitle level={3}>
                <CulturalText>幸运测试</CulturalText>
              </CulturalCardTitle>
            </CulturalCardHeader>
            <CulturalCardContent>
              <CulturalText>
                发现您的幸运数字和颜色，提升您的正能量。
                <LuckySymbol className='ml-2' animated />
              </CulturalText>
            </CulturalCardContent>
          </CulturalCard>
        </div>
      </section>

      {/* RTL布局演示 */}
      <section className='space-y-4'>
        <h2 className='text-xl font-semibold'>RTL布局支持演示</h2>
        <div className='flex flex-wrap gap-4'>
          <RTLButton variant='default'>普通按钮</RTLButton>
          <RTLNavButton direction='next'>下一步</RTLNavButton>
          <RTLNavButton direction='previous'>上一步</RTLNavButton>
        </div>
      </section>

      {/* 移动端优化演示 */}
      <section className='space-y-4'>
        <h2 className='text-xl font-semibold'>移动端优化演示</h2>

        {/* 优化的输入框 */}
        <div className='space-y-2'>
          <label className='block text-sm font-medium'>
            <CulturalText>优化的输入框</CulturalText>
          </label>
          <MobileOptimizedInput
            value={inputValue}
            onChange={setInputValue}
            placeholder='请输入您的姓名...'
            className='w-full'
          />
        </div>

        {/* 优化的按钮 */}
        <div className='flex flex-wrap gap-4'>
          <MobileOptimizedButton size='sm'>小按钮</MobileOptimizedButton>
          <MobileOptimizedButton size='md'>中等按钮</MobileOptimizedButton>
          <MobileOptimizedButton size='lg'>大按钮</MobileOptimizedButton>
        </div>

        {/* 优化的卡片 */}
        <div className='grid grid-cols-1 gap-4 sm:grid-cols-2'>
          <MobileOptimizedCard
            touchOptimized
            onClick={() => alert('卡片被点击了！')}
          >
            <h3 className='mb-2 font-semibold'>
              <CulturalText>触摸优化卡片</CulturalText>
            </h3>
            <CulturalText>
              这个卡片针对移动端触摸交互进行了优化，包括合适的触摸目标大小和反馈。
            </CulturalText>
          </MobileOptimizedCard>

          <MobileOptimizedCard performanceOptimized>
            <h3 className='mb-2 font-semibold'>
              <CulturalText>性能优化卡片</CulturalText>
            </h3>
            <CulturalText>
              这个卡片针对低端设备进行了性能优化，减少了动画和复杂效果。
            </CulturalText>
          </MobileOptimizedCard>
        </div>
      </section>

      {/* 符号系统演示 */}
      <section className='space-y-4'>
        <h2 className='text-xl font-semibold'>文化符号系统演示</h2>
        <div className='grid grid-cols-2 gap-4 sm:grid-cols-4'>
          <div className='rounded-lg border p-4 text-center'>
            <CulturalSymbol type='success' size='xl' className='mb-2 block' />
            <CulturalText>成功</CulturalText>
          </div>
          <div className='rounded-lg border p-4 text-center'>
            <CulturalSymbol type='error' size='xl' className='mb-2 block' />
            <CulturalText>错误</CulturalText>
          </div>
          <div className='rounded-lg border p-4 text-center'>
            <CulturalSymbol type='luck' size='xl' className='mb-2 block' />
            <CulturalText>幸运</CulturalText>
          </div>
          <div className='rounded-lg border p-4 text-center'>
            <CulturalSymbol
              type='spirituality'
              size='xl'
              className='mb-2 block'
            />
            <CulturalText>精神</CulturalText>
          </div>
        </div>
      </section>

      {/* 响应式测试区域 */}
      <section className='space-y-4'>
        <h2 className='text-xl font-semibold'>响应式测试</h2>
        <div className='rounded-lg bg-gray-100 p-4 dark:bg-gray-800'>
          <CulturalText>
            这个演示页面展示了完整的多语言架构实现，包括：
          </CulturalText>
          <ul className='mt-2 list-inside list-disc space-y-1'>
            <li>
              <CulturalText>6种语言支持（en、zh、es、pt、hi、ja）</CulturalText>
            </li>
            <li>
              <CulturalText>SEO友好的URL结构和hreflang标签</CulturalText>
            </li>
            <li>
              <CulturalText>RTL语言布局支持</CulturalText>
            </li>
            <li>
              <CulturalText>文化敏感的设计适配</CulturalText>
            </li>
            <li>
              <CulturalText>移动端触摸和性能优化</CulturalText>
            </li>
            <li>
              <CulturalText>可访问性增强</CulturalText>
            </li>
          </ul>
        </div>
      </section>

      {/* 调试信息 */}
      <section className='space-y-4'>
        <h2 className='text-xl font-semibold'>调试信息</h2>
        <div className='rounded-lg bg-blue-50 p-4 text-sm dark:bg-blue-900/20'>
          <CulturalText>当前页面使用了以下多语言功能组件：</CulturalText>
          <ul className='mt-2 list-inside list-disc space-y-1 text-xs'>
            <li>MobileMultilingualWrapper - 移动端多语言包装器</li>
            <li>CulturalProvider - 文化敏感设计提供者</li>
            <li>RTLProvider - RTL布局提供者</li>
            <li>LanguageSwitcher - 语言切换组件</li>
            <li>StructuredData - SEO结构化数据</li>
          </ul>
        </div>
      </section>
    </MobileMultilingualWrapper>
  );
}

/**
 * 简化的多语言卡片组件示例
 */
export function SimpleMultilingualCard({
  title,
  description,
  onClick,
}: {
  title: string;
  description: string;
  onClick?: () => void;
}) {
  return (
    <MobileOptimizedCard
      touchOptimized
      {...(onClick && { onClick })}
      className='transition-shadow hover:shadow-lg'
    >
      <div className='space-y-3'>
        <h3 className='flex items-center text-lg font-semibold'>
          <SpiritualSymbol className='mr-2' />
          <CulturalText>{title}</CulturalText>
        </h3>
        <CulturalText className='text-muted-foreground'>
          {description}
        </CulturalText>
        <div className='flex items-center justify-between'>
          <LuckySymbol animated />
          <RTLNavButton direction='next' size='sm'>
            开始测试
          </RTLNavButton>
        </div>
      </div>
    </MobileOptimizedCard>
  );
}

export default MultilingualDemo;
