'use client';

import { useEffect, useState } from 'react';

import { useMobileMultilingual } from '@/hooks/use-mobile-multilingual';
import { cn } from '@/lib/utils';

interface MobileMultilingualWrapperProps {
  children: React.ReactNode;
  className?: string;
  enableTouchOptimization?: boolean;
  enablePerformanceOptimization?: boolean;
  enableAccessibilityOptimization?: boolean;
}

export function MobileMultilingualWrapper({
  children,
  className,
  enableTouchOptimization = true,
  enablePerformanceOptimization = true,
  enableAccessibilityOptimization = true,
}: MobileMultilingualWrapperProps) {
  const {
    isMobile,
    isLowEndDevice,
    isHighContrast,
    isReducedMotion,
    getTouchTargetSize,
    getMobileFontSize,
    getMobileLineHeight,
    getMobileLetterSpacing,
    getPerformanceConfig,
    getAccessibilityConfig,
  } = useMobileMultilingual();

  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return <div className={className}>{children}</div>;
  }

  const performanceConfig = getPerformanceConfig();
  const accessibilityConfig = getAccessibilityConfig();

  // 构建CSS变量
  const cssVars = {
    '--mobile-touch-target': enableTouchOptimization
      ? `${getTouchTargetSize(44)}px`
      : '44px',
    '--mobile-font-size': `${getMobileFontSize(16)}px`,
    '--mobile-line-height': getMobileLineHeight(1.6),
    '--mobile-letter-spacing': getMobileLetterSpacing('0.01em'),
    '--mobile-focus-ring': `${accessibilityConfig.focusRingSize}px`,
    '--mobile-animation-duration': isReducedMotion ? '0ms' : '300ms',
  } as React.CSSProperties;

  // 构建类名
  const wrapperClasses = cn(
    // 基础类
    'mobile-multilingual-wrapper',

    // 移动端特定类
    isMobile && 'mobile-optimized',

    // 性能优化类
    enablePerformanceOptimization && isLowEndDevice && 'low-end-device',
    enablePerformanceOptimization &&
      !performanceConfig.enableAnimations &&
      'no-animations',

    // 可访问性优化类
    enableAccessibilityOptimization && isHighContrast && 'high-contrast',
    enableAccessibilityOptimization && isReducedMotion && 'reduced-motion',
    enableAccessibilityOptimization &&
      accessibilityConfig.screenReaderOptimized &&
      'screen-reader-optimized',

    // 触摸优化类
    enableTouchOptimization && isMobile && 'touch-optimized',

    className
  );

  return (
    <div
      className={wrapperClasses}
      style={cssVars}
      // 可访问性属性
      {...(enableAccessibilityOptimization && {
        'aria-live': 'polite',
        'data-mobile-optimized': isMobile,
        'data-language-direction': accessibilityConfig.languageSpecific
          .announceDirection
          ? 'rtl'
          : 'ltr',
        'data-language': accessibilityConfig.languageSpecific.announceLanguage,
      })}
    >
      {children}
    </div>
  );
}

// 移动端优化的文本组件
interface MobileOptimizedTextProps {
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  adaptive?: boolean;
}

export function MobileOptimizedText({
  children,
  className,
  size = 'md',
  adaptive = true,
}: MobileOptimizedTextProps) {
  const {
    isMobile,
    getMobileFontSize,
    getMobileLineHeight,
    getMobileLetterSpacing,
  } = useMobileMultilingual();

  const baseSizes = {
    sm: 14,
    md: 16,
    lg: 18,
    xl: 20,
  };

  const fontSize = adaptive
    ? getMobileFontSize(baseSizes[size])
    : baseSizes[size];
  const lineHeight = adaptive ? getMobileLineHeight(1.6) : 1.6;
  const letterSpacing = adaptive ? getMobileLetterSpacing('0.01em') : '0.01em';

  return (
    <span
      className={cn('mobile-optimized-text', className)}
      style={{
        fontSize: `${fontSize}px`,
        lineHeight,
        letterSpacing,
      }}
    >
      {children}
    </span>
  );
}

// 移动端优化的按钮组件
interface MobileOptimizedButtonProps {
  children: React.ReactNode;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  touchOptimized?: boolean;
  onClick?: () => void;
  disabled?: boolean;
  type?: 'button' | 'submit' | 'reset';
}

export function MobileOptimizedButton({
  children,
  className,
  size = 'md',
  touchOptimized = true,
  onClick,
  disabled = false,
  type = 'button',
}: MobileOptimizedButtonProps) {
  const {
    isMobile,
    getTouchTargetSize,
    getTouchSpacing,
    getAccessibilityConfig,
  } = useMobileMultilingual();

  const baseSizes = {
    sm: 36,
    md: 44,
    lg: 52,
  };

  const buttonHeight = touchOptimized
    ? getTouchTargetSize(baseSizes[size])
    : baseSizes[size];
  const accessibilityConfig = getAccessibilityConfig();

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={cn(
        'mobile-optimized-button',
        'inline-flex items-center justify-center',
        'rounded-md font-medium transition-colors',
        'focus:outline-none focus:ring-2 focus:ring-offset-2',
        'disabled:pointer-events-none disabled:opacity-50',
        touchOptimized && isMobile && 'touch-optimized',
        className
      )}
      style={
        {
          minHeight: `${buttonHeight}px`,
          padding: `${getTouchSpacing(8)}px ${getTouchSpacing(16)}px`,
          '--focus-ring-width': `${accessibilityConfig.focusRingSize}px`,
        } as React.CSSProperties
      }
      // 可访问性属性
      aria-label={typeof children === 'string' ? children : undefined}
      tabIndex={disabled ? -1 : 0}
    >
      <MobileOptimizedText adaptive={touchOptimized}>
        {children}
      </MobileOptimizedText>
    </button>
  );
}

// 移动端优化的输入框组件
interface MobileOptimizedInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  type?: 'text' | 'email' | 'tel' | 'url' | 'search';
  disabled?: boolean;
  required?: boolean;
  autoComplete?: string;
}

export function MobileOptimizedInput({
  value,
  onChange,
  placeholder,
  className,
  type = 'text',
  disabled = false,
  required = false,
  autoComplete,
}: MobileOptimizedInputProps) {
  const {
    isMobile,
    getTouchTargetSize,
    getInputMethodConfig,
    getVirtualKeyboardConfig,
    getAccessibilityConfig,
  } = useMobileMultilingual();

  const inputMethodConfig = getInputMethodConfig();
  const keyboardConfig = getVirtualKeyboardConfig();
  const accessibilityConfig = getAccessibilityConfig();

  const inputHeight = getTouchTargetSize(44);

  return (
    <input
      type={type}
      value={value}
      onChange={e => onChange(e.target.value)}
      placeholder={placeholder}
      disabled={disabled}
      required={required}
      autoComplete={autoComplete || inputMethodConfig.autoComplete}
      spellCheck={inputMethodConfig.spellCheck}
      lang={inputMethodConfig.lang}
      inputMode={inputMethodConfig.inputMode}
      className={cn(
        'mobile-optimized-input',
        'border-input w-full rounded-md border',
        'bg-background px-3 py-2',
        'text-sm ring-offset-background',
        'focus:ring-ring focus:outline-none focus:ring-2 focus:ring-offset-2',
        'disabled:cursor-not-allowed disabled:opacity-50',
        isMobile && 'mobile-input',
        className
      )}
      style={
        {
          minHeight: `${inputHeight}px`,
          fontSize: isMobile ? '16px' : '14px', // 防止iOS缩放
          '--focus-ring-width': `${accessibilityConfig.focusRingSize}px`,
        } as React.CSSProperties
      }
      // 移动端特定属性
      {...(isMobile &&
        keyboardConfig.preventViewportZoom && {
          'data-prevent-zoom': 'true',
        })}
    />
  );
}

// 移动端优化的卡片组件
interface MobileOptimizedCardProps {
  children: React.ReactNode;
  className?: string;
  touchOptimized?: boolean;
  performanceOptimized?: boolean;
  onClick?: () => void;
}

export function MobileOptimizedCard({
  children,
  className,
  touchOptimized = true,
  performanceOptimized = true,
  onClick,
}: MobileOptimizedCardProps) {
  const { isMobile, isLowEndDevice, getTouchSpacing, getPerformanceConfig } =
    useMobileMultilingual();

  const performanceConfig = getPerformanceConfig();
  const padding = getTouchSpacing(16);

  return (
    <div
      className={cn(
        'mobile-optimized-card',
        'text-card-foreground rounded-lg border bg-card shadow-sm',
        touchOptimized && isMobile && 'touch-optimized',
        performanceOptimized && isLowEndDevice && 'performance-optimized',
        onClick && 'cursor-pointer transition-shadow hover:shadow-md',
        !performanceConfig.enableAnimations && 'no-animations',
        className
      )}
      style={{
        padding: `${padding}px`,
      }}
      onClick={onClick}
      // 可访问性
      {...(onClick && {
        role: 'button',
        tabIndex: 0,
        onKeyDown: e => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onClick();
          }
        },
      })}
    >
      {children}
    </div>
  );
}

// 导出所有组件
export {
  MobileMultilingualWrapper,
  MobileOptimizedText,
  MobileOptimizedButton,
  MobileOptimizedInput,
  MobileOptimizedCard,
};
