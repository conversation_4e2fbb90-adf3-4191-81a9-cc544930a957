import { cva, type VariantProps } from 'class-variance-authority';
import * as React from 'react';

import { cn } from '@/lib/utils';

// Input组件变体
const inputVariants = cva(
  'flex w-full rounded-lg border bg-background px-3 py-2 text-sm transition-all duration-200 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-foreground-muted focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50',
  {
    variants: {
      variant: {
        default:
          'border-border focus-visible:ring-2 focus-visible:ring-mystical-500 focus-visible:ring-offset-2',
        mystical:
          'border-mystical-300 focus-visible:border-mystical-500 focus-visible:ring-2 focus-visible:ring-mystical-500 focus-visible:ring-offset-2 focus-visible:shadow-mystical',
        golden:
          'border-gold-300 focus-visible:border-gold-500 focus-visible:ring-2 focus-visible:ring-gold-500 focus-visible:ring-offset-2 focus-visible:shadow-gold',
        ghost:
          'border-transparent bg-background-secondary focus-visible:border-mystical-300 focus-visible:bg-background',
      },
      size: {
        sm: 'h-8 px-2 text-xs',
        default: 'h-10 px-3',
        lg: 'h-12 px-4 text-base',
      },
      state: {
        default: '',
        error:
          'border-red-500 focus-visible:ring-red-500 text-red-900 placeholder:text-red-400',
        success:
          'border-green-500 focus-visible:ring-green-500 text-green-900 placeholder:text-green-400',
        warning:
          'border-yellow-500 focus-visible:ring-yellow-500 text-yellow-900 placeholder:text-yellow-400',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      state: 'default',
    },
  }
);

// Input组件接口
export interface InputProps
  extends React.InputHTMLAttributes<HTMLInputElement>,
    VariantProps<typeof inputVariants> {
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  error?: string;
  helperText?: string;
  label?: string;
}

// Input组件
const Input = React.forwardRef<HTMLInputElement, InputProps>(
  (
    {
      className,
      variant,
      size,
      state,
      type = 'text',
      leftIcon,
      rightIcon,
      error,
      helperText,
      label,
      id,
      ...props
    },
    ref
  ) => {
    const inputId = id || React.useId();
    const hasError = error || state === 'error';
    const actualState = hasError ? 'error' : state;

    return (
      <div className='w-full'>
        {label && (
          <label
            htmlFor={inputId}
            className='mb-2 block text-sm font-medium text-foreground'
          >
            {label}
          </label>
        )}
        <div className='relative'>
          {leftIcon && (
            <div className='absolute left-3 top-1/2 -translate-y-1/2 text-foreground-muted'>
              {leftIcon}
            </div>
          )}
          <input
            id={inputId}
            type={type}
            className={cn(
              inputVariants({ variant, size, state: actualState }),
              leftIcon && 'pl-10',
              rightIcon && 'pr-10',
              className
            )}
            ref={ref}
            {...props}
          />
          {rightIcon && (
            <div className='absolute right-3 top-1/2 -translate-y-1/2 text-foreground-muted'>
              {rightIcon}
            </div>
          )}
        </div>
        {(error || helperText) && (
          <p
            className={cn(
              'mt-2 text-xs',
              hasError ? 'text-red-600' : 'text-foreground-muted'
            )}
          >
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);
Input.displayName = 'Input';

// Textarea组件变体
const textareaVariants = cva(
  'flex min-h-[80px] w-full rounded-lg border bg-background px-3 py-2 text-sm transition-all duration-200 placeholder:text-foreground-muted focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 resize-none',
  {
    variants: {
      variant: {
        default:
          'border-border focus-visible:ring-2 focus-visible:ring-mystical-500 focus-visible:ring-offset-2',
        mystical:
          'border-mystical-300 focus-visible:border-mystical-500 focus-visible:ring-2 focus-visible:ring-mystical-500 focus-visible:ring-offset-2 focus-visible:shadow-mystical',
        golden:
          'border-gold-300 focus-visible:border-gold-500 focus-visible:ring-2 focus-visible:ring-gold-500 focus-visible:ring-offset-2 focus-visible:shadow-gold',
        ghost:
          'border-transparent bg-background-secondary focus-visible:border-mystical-300 focus-visible:bg-background',
      },
      size: {
        sm: 'min-h-[60px] px-2 py-1 text-xs',
        default: 'min-h-[80px] px-3 py-2',
        lg: 'min-h-[120px] px-4 py-3 text-base',
      },
      state: {
        default: '',
        error:
          'border-red-500 focus-visible:ring-red-500 text-red-900 placeholder:text-red-400',
        success:
          'border-green-500 focus-visible:ring-green-500 text-green-900 placeholder:text-green-400',
        warning:
          'border-yellow-500 focus-visible:ring-yellow-500 text-yellow-900 placeholder:text-yellow-400',
      },
      resize: {
        none: 'resize-none',
        vertical: 'resize-y',
        horizontal: 'resize-x',
        both: 'resize',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
      state: 'default',
      resize: 'vertical',
    },
  }
);

// Textarea组件接口
export interface TextareaProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement>,
    VariantProps<typeof textareaVariants> {
  error?: string;
  helperText?: string;
  label?: string;
}

// Textarea组件
const Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(
  (
    {
      className,
      variant,
      size,
      state,
      resize,
      error,
      helperText,
      label,
      id,
      ...props
    },
    ref
  ) => {
    const textareaId = id || React.useId();
    const hasError = error || state === 'error';
    const actualState = hasError ? 'error' : state;

    return (
      <div className='w-full'>
        {label && (
          <label
            htmlFor={textareaId}
            className='mb-2 block text-sm font-medium text-foreground'
          >
            {label}
          </label>
        )}
        <textarea
          id={textareaId}
          className={cn(
            textareaVariants({ variant, size, state: actualState, resize }),
            className
          )}
          ref={ref}
          {...props}
        />
        {(error || helperText) && (
          <p
            className={cn(
              'mt-2 text-xs',
              hasError ? 'text-red-600' : 'text-foreground-muted'
            )}
          >
            {error || helperText}
          </p>
        )}
      </div>
    );
  }
);
Textarea.displayName = 'Textarea';

// Label组件
const Label = React.forwardRef<
  HTMLLabelElement,
  React.LabelHTMLAttributes<HTMLLabelElement> & {
    required?: boolean;
  }
>(({ className, required, children, ...props }, ref) => (
  <label
    ref={ref}
    className={cn(
      'text-sm font-medium leading-none text-foreground peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
      className
    )}
    {...props}
  >
    {children}
    {required && <span className='ml-1 text-red-500'>*</span>}
  </label>
));
Label.displayName = 'Label';

// FormField组件 - 表单字段容器
const FormField = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    error?: string;
    helperText?: string;
  }
>(({ className, error, helperText, children, ...props }, ref) => (
  <div ref={ref} className={cn('space-y-2', className)} {...props}>
    {children}
    {(error || helperText) && (
      <p
        className={cn(
          'text-xs',
          error ? 'text-red-600' : 'text-foreground-muted'
        )}
      >
        {error || helperText}
      </p>
    )}
  </div>
));
FormField.displayName = 'FormField';

export { Input, Textarea, Label, FormField, inputVariants, textareaVariants };
