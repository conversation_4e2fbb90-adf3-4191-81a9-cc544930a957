import { cva, type VariantProps } from 'class-variance-authority';
import * as React from 'react';

import { cn } from '@/lib/utils';

// Container组件变体
const containerVariants = cva('mx-auto w-full', {
  variants: {
    size: {
      sm: 'max-w-screen-sm',
      md: 'max-w-screen-md',
      lg: 'max-w-screen-lg',
      xl: 'max-w-screen-xl',
      '2xl': 'max-w-screen-2xl',
      full: 'max-w-full',
      content: 'max-w-4xl',
      reading: 'max-w-3xl',
    },
    padding: {
      none: '',
      sm: 'px-4 sm:px-6',
      md: 'px-4 sm:px-6 lg:px-8',
      lg: 'px-6 sm:px-8 lg:px-12',
    },
  },
  defaultVariants: {
    size: 'xl',
    padding: 'md',
  },
});

// Grid组件变体
const gridVariants = cva('grid', {
  variants: {
    cols: {
      1: 'grid-cols-1',
      2: 'grid-cols-1 md:grid-cols-2',
      3: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
      4: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
      5: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5',
      6: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6',
      auto: 'grid-cols-[repeat(auto-fit,minmax(250px,1fr))]',
      'auto-sm': 'grid-cols-[repeat(auto-fit,minmax(200px,1fr))]',
      'auto-lg': 'grid-cols-[repeat(auto-fit,minmax(300px,1fr))]',
    },
    gap: {
      none: 'gap-0',
      xs: 'gap-2',
      sm: 'gap-4',
      md: 'gap-6',
      lg: 'gap-8',
      xl: 'gap-12',
    },
  },
  defaultVariants: {
    cols: 3,
    gap: 'md',
  },
});

// Stack组件变体
const stackVariants = cva('flex', {
  variants: {
    direction: {
      row: 'flex-row',
      'row-reverse': 'flex-row-reverse',
      col: 'flex-col',
      'col-reverse': 'flex-col-reverse',
    },
    align: {
      start: 'items-start',
      center: 'items-center',
      end: 'items-end',
      stretch: 'items-stretch',
      baseline: 'items-baseline',
    },
    justify: {
      start: 'justify-start',
      center: 'justify-center',
      end: 'justify-end',
      between: 'justify-between',
      around: 'justify-around',
      evenly: 'justify-evenly',
    },
    gap: {
      none: 'gap-0',
      xs: 'gap-1',
      sm: 'gap-2',
      md: 'gap-4',
      lg: 'gap-6',
      xl: 'gap-8',
    },
    wrap: {
      nowrap: 'flex-nowrap',
      wrap: 'flex-wrap',
      'wrap-reverse': 'flex-wrap-reverse',
    },
  },
  defaultVariants: {
    direction: 'col',
    align: 'stretch',
    justify: 'start',
    gap: 'md',
    wrap: 'nowrap',
  },
});

// Container组件接口
export interface ContainerProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof containerVariants> {}

// Grid组件接口
export interface GridProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof gridVariants> {}

// Stack组件接口
export interface StackProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof stackVariants> {}

// Container组件
const Container = React.forwardRef<HTMLDivElement, ContainerProps>(
  ({ className, size, padding, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(containerVariants({ size, padding }), className)}
      {...props}
    />
  )
);
Container.displayName = 'Container';

// Grid组件
const Grid = React.forwardRef<HTMLDivElement, GridProps>(
  ({ className, cols, gap, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(gridVariants({ cols, gap }), className)}
      {...props}
    />
  )
);
Grid.displayName = 'Grid';

// Stack组件
const Stack = React.forwardRef<HTMLDivElement, StackProps>(
  ({ className, direction, align, justify, gap, wrap, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        stackVariants({ direction, align, justify, gap, wrap }),
        className
      )}
      {...props}
    />
  )
);
Stack.displayName = 'Stack';

// Flex组件 - Stack的别名，更直观
const Flex = Stack;

// Center组件 - 居中布局
const Center = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    inline?: boolean;
  }
>(({ className, inline = false, ...props }, ref) => (
  <div
    ref={ref}
    className={cn(
      inline ? 'inline-flex' : 'flex',
      'items-center justify-center',
      className
    )}
    {...props}
  />
));
Center.displayName = 'Center';

// Spacer组件 - 弹性空间
const Spacer = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div ref={ref} className={cn('flex-1', className)} {...props} />
));
Spacer.displayName = 'Spacer';

// Divider组件 - 分隔线
const Divider = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    orientation?: 'horizontal' | 'vertical';
    variant?: 'solid' | 'dashed' | 'dotted' | 'mystical';
  }
>(
  (
    { className, orientation = 'horizontal', variant = 'solid', ...props },
    ref
  ) => {
    const orientationClasses = {
      horizontal: 'w-full h-px',
      vertical: 'h-full w-px',
    };

    const variantClasses = {
      solid: 'bg-border',
      dashed: 'border-t border-dashed border-border',
      dotted: 'border-t border-dotted border-border',
      mystical:
        'bg-gradient-to-r from-transparent via-mystical-400 to-transparent',
    };

    return (
      <div
        ref={ref}
        className={cn(
          orientationClasses[orientation],
          variantClasses[variant],
          className
        )}
        {...props}
      />
    );
  }
);
Divider.displayName = 'Divider';

// AspectRatio组件 - 宽高比容器
const AspectRatio = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    ratio?: number;
  }
>(({ className, ratio = 16 / 9, children, ...props }, ref) => (
  <div
    ref={ref}
    className={cn('relative w-full', className)}
    style={{ aspectRatio: ratio }}
    {...props}
  >
    {children}
  </div>
));
AspectRatio.displayName = 'AspectRatio';

// Section组件 - 页面区块
const Section = React.forwardRef<
  HTMLElement,
  React.HTMLAttributes<HTMLElement> & {
    as?: 'section' | 'div' | 'article' | 'main' | 'aside';
    padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  }
>(({ className, as = 'section', padding = 'lg', ...props }, ref) => {
  const Component = as;

  const paddingClasses = {
    none: '',
    sm: 'py-8',
    md: 'py-12',
    lg: 'py-16',
    xl: 'py-24',
  };

  return (
    <Component
      ref={ref}
      className={cn(paddingClasses[padding], className)}
      {...props}
    />
  );
});
Section.displayName = 'Section';

export {
  Container,
  Grid,
  Stack,
  Flex,
  Center,
  Spacer,
  Divider,
  AspectRatio,
  Section,
  containerVariants,
  gridVariants,
  stackVariants,
};
