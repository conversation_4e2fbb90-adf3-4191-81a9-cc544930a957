import { cva, type VariantProps } from 'class-variance-authority';
import { X } from 'lucide-react';
import * as React from 'react';

import { cn } from '@/lib/utils';

// Badge组件变体
const badgeVariants = cva(
  'inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  {
    variants: {
      variant: {
        default:
          'border-transparent bg-mystical-600 text-white hover:bg-mystical-700',
        secondary:
          'border-transparent bg-mystical-100 text-mystical-800 hover:bg-mystical-200 dark:bg-mystical-800/20 dark:text-mystical-200',
        destructive:
          'border-transparent bg-red-600 text-white hover:bg-red-700',
        outline:
          'border-mystical-300 text-mystical-700 hover:bg-mystical-50 dark:border-mystical-600 dark:text-mystical-400',
        mystical:
          'border-transparent bg-gradient-to-r from-mystical-500 to-mystical-600 text-white hover:from-mystical-600 hover:to-mystical-700',
        golden:
          'border-transparent bg-gradient-to-r from-gold-500 to-gold-600 text-white hover:from-gold-600 hover:to-gold-700',
        success:
          'border-transparent bg-green-600 text-white hover:bg-green-700',
        warning:
          'border-transparent bg-yellow-600 text-white hover:bg-yellow-700',
        info: 'border-transparent bg-blue-600 text-white hover:bg-blue-700',
        // 星座主题
        fire: 'border-transparent bg-red-500 text-white hover:bg-red-600',
        earth: 'border-transparent bg-green-500 text-white hover:bg-green-600',
        air: 'border-transparent bg-blue-500 text-white hover:bg-blue-600',
        water:
          'border-transparent bg-purple-500 text-white hover:bg-purple-600',
      },
      size: {
        sm: 'px-2 py-0.5 text-xs',
        default: 'px-2.5 py-0.5 text-xs',
        lg: 'px-3 py-1 text-sm',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

// Tag组件变体
const tagVariants = cva(
  'inline-flex items-center rounded-md border px-2 py-1 text-xs font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2',
  {
    variants: {
      variant: {
        default:
          'border-mystical-200 bg-mystical-50 text-mystical-700 hover:bg-mystical-100 dark:border-mystical-700 dark:bg-mystical-900/20 dark:text-mystical-300',
        secondary:
          'border-border bg-background-secondary text-foreground-secondary hover:bg-background-tertiary',
        outline:
          'border-mystical-300 text-mystical-700 hover:bg-mystical-50 dark:border-mystical-600 dark:text-mystical-400',
        mystical:
          'border-mystical-300 bg-mystical-100 text-mystical-800 hover:bg-mystical-200 dark:border-mystical-600 dark:bg-mystical-800/20 dark:text-mystical-200',
        golden:
          'border-gold-300 bg-gold-100 text-gold-800 hover:bg-gold-200 dark:border-gold-600 dark:bg-gold-800/20 dark:text-gold-200',
        success:
          'border-green-300 bg-green-100 text-green-800 hover:bg-green-200 dark:border-green-600 dark:bg-green-800/20 dark:text-green-200',
        warning:
          'border-yellow-300 bg-yellow-100 text-yellow-800 hover:bg-yellow-200 dark:border-yellow-600 dark:bg-yellow-800/20 dark:text-yellow-200',
        error:
          'border-red-300 bg-red-100 text-red-800 hover:bg-red-200 dark:border-red-600 dark:bg-red-800/20 dark:text-red-200',
        info: 'border-blue-300 bg-blue-100 text-blue-800 hover:bg-blue-200 dark:border-blue-600 dark:bg-blue-800/20 dark:text-blue-200',
      },
      size: {
        sm: 'px-1.5 py-0.5 text-xs',
        default: 'px-2 py-1 text-xs',
        lg: 'px-3 py-1.5 text-sm',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

// Badge组件接口
export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {
  dot?: boolean;
}

// Tag组件接口
export interface TagProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof tagVariants> {
  closable?: boolean;
  onClose?: () => void;
  icon?: React.ReactNode;
}

// Badge组件
const Badge = React.forwardRef<HTMLDivElement, BadgeProps>(
  ({ className, variant, size, dot = false, children, ...props }, ref) => (
    <div
      ref={ref}
      className={cn(
        badgeVariants({ variant, size }),
        dot && 'px-1.5 py-1.5 text-[0px]',
        className
      )}
      {...props}
    >
      {dot ? <span className='sr-only'>{children}</span> : children}
    </div>
  )
);
Badge.displayName = 'Badge';

// Tag组件
const Tag = React.forwardRef<HTMLDivElement, TagProps>(
  (
    {
      className,
      variant,
      size,
      closable = false,
      onClose,
      icon,
      children,
      ...props
    },
    ref
  ) => (
    <div
      ref={ref}
      className={cn(tagVariants({ variant, size }), className)}
      {...props}
    >
      {icon && <span className='mr-1'>{icon}</span>}
      <span>{children}</span>
      {closable && (
        <button
          type='button'
          className='ml-1 inline-flex h-4 w-4 items-center justify-center rounded-full hover:bg-black/10 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white dark:hover:bg-white/10'
          onClick={onClose}
        >
          <span className='sr-only'>Remove</span>
          <X className='h-3 w-3' />
        </button>
      )}
    </div>
  )
);
Tag.displayName = 'Tag';

// StatusBadge组件 - 状态徽章
const StatusBadge = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    status: 'online' | 'offline' | 'away' | 'busy';
    showText?: boolean;
  }
>(({ className, status, showText = false, ...props }, ref) => {
  const statusConfig = {
    online: { color: 'bg-green-500', text: 'Online' },
    offline: { color: 'bg-gray-500', text: 'Offline' },
    away: { color: 'bg-yellow-500', text: 'Away' },
    busy: { color: 'bg-red-500', text: 'Busy' },
  };

  const config = statusConfig[status];

  return (
    <div
      ref={ref}
      className={cn('inline-flex items-center gap-2', className)}
      {...props}
    >
      <div className={cn('h-2 w-2 rounded-full', config.color)} />
      {showText && (
        <span className='text-xs font-medium text-foreground-secondary'>
          {config.text}
        </span>
      )}
    </div>
  );
});
StatusBadge.displayName = 'StatusBadge';

// NotificationBadge组件 - 通知徽章
const NotificationBadge = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    count?: number;
    max?: number;
    dot?: boolean;
    variant?: 'default' | 'mystical' | 'golden';
  }
>(
  (
    {
      className,
      count = 0,
      max = 99,
      dot = false,
      variant = 'default',
      ...props
    },
    ref
  ) => {
    if (count === 0 && !dot) return null;

    const displayCount = count > max ? `${max}+` : count.toString();

    const variantClasses = {
      default: 'bg-red-500 text-white',
      mystical: 'bg-mystical-500 text-white',
      golden: 'bg-gold-500 text-white',
    };

    return (
      <div
        ref={ref}
        className={cn(
          'absolute -right-1 -top-1 flex h-5 w-5 items-center justify-center rounded-full text-xs font-bold',
          variantClasses[variant],
          dot && 'h-2 w-2',
          className
        )}
        {...props}
      >
        {!dot && <span>{displayCount}</span>}
      </div>
    );
  }
);
NotificationBadge.displayName = 'NotificationBadge';

// TagGroup组件 - 标签组
const TagGroup = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    tags: Array<{
      id: string;
      label: string;
      variant?: VariantProps<typeof tagVariants>['variant'];
      closable?: boolean;
    }>;
    onTagClose?: (id: string) => void;
    maxVisible?: number;
  }
>(({ className, tags, onTagClose, maxVisible, ...props }, ref) => {
  const visibleTags = maxVisible ? tags.slice(0, maxVisible) : tags;
  const hiddenCount = maxVisible ? Math.max(0, tags.length - maxVisible) : 0;

  return (
    <div ref={ref} className={cn('flex flex-wrap gap-2', className)} {...props}>
      {visibleTags.map(tag => (
        <Tag
          key={tag.id}
          variant={tag.variant}
          closable={tag.closable}
          onClose={() => onTagClose?.(tag.id)}
        >
          {tag.label}
        </Tag>
      ))}
      {hiddenCount > 0 && <Tag variant='secondary'>+{hiddenCount} more</Tag>}
    </div>
  );
});
TagGroup.displayName = 'TagGroup';

export {
  Badge,
  Tag,
  StatusBadge,
  NotificationBadge,
  TagGroup,
  badgeVariants,
  tagVariants,
};
