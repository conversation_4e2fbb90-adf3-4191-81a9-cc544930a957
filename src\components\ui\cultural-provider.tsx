'use client';

import { useLocale } from 'next-intl';
import { createContext, useContext, useEffect, useState } from 'react';

import { languageConfig, type Locale } from '@/i18n';

// 文化敏感配置类型
interface CulturalConfig {
  // 字体配置
  fontFamily: string;
  lineHeight: number;
  letterSpacing: string;
  wordBreak?: string;

  // 文字长度适配
  expansionFactor: number;

  // 颜色偏好
  colorPreferences: {
    primary: string;
    secondary: string;
    accent: string;
    lucky: string;
    unlucky: string;
  };

  // 符号系统
  symbols: {
    success: string;
    error: string;
    warning: string;
    info: string;
    luck: string;
    spirituality: string;
  };

  // 日期和时间格式
  dateFormat: string;
  timeFormat: '12h' | '24h';

  // 数字格式
  numberFormat: string;

  // 货币
  currency: string;

  // 文化特定行为
  behaviors: {
    showAnimations: boolean;
    preferredColorScheme: 'light' | 'dark' | 'system';
    preferredIconStyle: 'minimal' | 'detailed' | 'symbolic';
    preferredInteractionStyle: 'direct' | 'indirect';
  };
}

// 默认文化配置
const defaultCulturalConfig: CulturalConfig = {
  fontFamily: 'latin',
  lineHeight: 1.6,
  letterSpacing: '0.01em',
  expansionFactor: 1.0,
  colorPreferences: {
    primary: 'mystical-500',
    secondary: 'gold-500',
    accent: 'mystical-300',
    lucky: 'gold-400',
    unlucky: 'dark-500',
  },
  symbols: {
    success: '✓',
    error: '✗',
    warning: '⚠',
    info: 'ℹ',
    luck: '🍀',
    spirituality: '✨',
  },
  dateFormat: 'MM/dd/yyyy',
  timeFormat: '12h',
  numberFormat: 'en-US',
  currency: 'USD',
  behaviors: {
    showAnimations: true,
    preferredColorScheme: 'light',
    preferredIconStyle: 'minimal',
    preferredInteractionStyle: 'direct',
  },
};

// 文化配置映射
const culturalConfigs: Record<string, Partial<CulturalConfig>> = {
  // 英语 (默认)
  en: {
    fontFamily: 'latin',
    lineHeight: 1.6,
    letterSpacing: '0.01em',
    expansionFactor: 1.0,
    colorPreferences: {
      primary: 'mystical-500',
      secondary: 'gold-500',
      accent: 'mystical-300',
      lucky: 'gold-400',
      unlucky: 'dark-500',
    },
    symbols: {
      success: '✓',
      error: '✗',
      warning: '⚠',
      info: 'ℹ',
      luck: '🍀',
      spirituality: '✨',
    },
    dateFormat: 'MM/dd/yyyy',
    timeFormat: '12h',
    numberFormat: 'en-US',
    currency: 'USD',
    behaviors: {
      showAnimations: true,
      preferredColorScheme: 'light',
      preferredIconStyle: 'minimal',
      preferredInteractionStyle: 'direct',
    },
  },

  // 简体中文
  'zh-CN': {
    fontFamily: 'chinese',
    lineHeight: 1.7,
    letterSpacing: '0.05em',
    wordBreak: 'break-all',
    expansionFactor: 0.8,
    colorPreferences: {
      primary: 'mystical-600', // 更深的紫色
      secondary: 'gold-600', // 更深的金色
      accent: 'mystical-300',
      lucky: 'gold-500', // 金色在中国文化中代表好运
      unlucky: 'dark-500',
    },
    symbols: {
      success: '✓',
      error: '✗',
      warning: '⚠',
      info: 'ℹ',
      luck: '🧧', // 红包象征好运
      spirituality: '☯', // 阴阳符号
    },
    dateFormat: 'yyyy年MM月dd日',
    timeFormat: '24h',
    numberFormat: 'zh-CN',
    currency: 'CNY',
    behaviors: {
      showAnimations: true,
      preferredColorScheme: 'light',
      preferredIconStyle: 'detailed',
      preferredInteractionStyle: 'indirect',
    },
  },

  // 繁体中文
  'zh-TW': {
    fontFamily: 'chinese',
    lineHeight: 1.7,
    letterSpacing: '0.05em',
    wordBreak: 'break-all',
    expansionFactor: 0.8,
    colorPreferences: {
      primary: 'mystical-600', // 更深的紫色
      secondary: 'gold-600', // 更深的金色
      accent: 'mystical-300',
      lucky: 'gold-500', // 金色在台湾文化中代表好运
      unlucky: 'dark-500',
    },
    symbols: {
      success: '✓',
      error: '✗',
      warning: '⚠',
      info: 'ℹ',
      luck: '🧧', // 红包象征好运
      spirituality: '☯', // 阴阳符号
    },
    dateFormat: 'yyyy年MM月dd日',
    timeFormat: '24h',
    numberFormat: 'zh-TW',
    currency: 'TWD',
    behaviors: {
      showAnimations: true,
      preferredColorScheme: 'light',
      preferredIconStyle: 'detailed',
      preferredInteractionStyle: 'indirect',
    },
  },

  // 西班牙语
  es: {
    fontFamily: 'latin',
    lineHeight: 1.6,
    letterSpacing: '0.01em',
    expansionFactor: 1.25,
    colorPreferences: {
      primary: 'mystical-500',
      secondary: 'gold-500',
      accent: 'mystical-300',
      lucky: 'gold-400',
      unlucky: 'dark-500',
    },
    symbols: {
      success: '✓',
      error: '✗',
      warning: '⚠',
      info: 'ℹ',
      luck: '🍀',
      spirituality: '✝', // 十字架
    },
    dateFormat: 'dd/MM/yyyy',
    timeFormat: '24h',
    numberFormat: 'es-ES',
    currency: 'EUR',
    behaviors: {
      showAnimations: true,
      preferredColorScheme: 'light',
      preferredIconStyle: 'detailed',
      preferredInteractionStyle: 'direct',
    },
  },

  // 葡萄牙语
  pt: {
    fontFamily: 'latin',
    lineHeight: 1.6,
    letterSpacing: '0.01em',
    expansionFactor: 1.2,
    colorPreferences: {
      primary: 'mystical-500',
      secondary: 'gold-500',
      accent: 'mystical-300',
      lucky: 'gold-400',
      unlucky: 'dark-500',
    },
    symbols: {
      success: '✓',
      error: '✗',
      warning: '⚠',
      info: 'ℹ',
      luck: '🍀',
      spirituality: '✝', // 十字架
    },
    dateFormat: 'dd/MM/yyyy',
    timeFormat: '24h',
    numberFormat: 'pt-BR',
    currency: 'BRL',
    behaviors: {
      showAnimations: true,
      preferredColorScheme: 'light',
      preferredIconStyle: 'detailed',
      preferredInteractionStyle: 'direct',
    },
  },

  // 印地语
  hi: {
    fontFamily: 'hindi',
    lineHeight: 1.8,
    letterSpacing: '0.02em',
    expansionFactor: 1.4,
    colorPreferences: {
      primary: 'mystical-500',
      secondary: 'gold-500',
      accent: 'mystical-300',
      lucky: 'gold-400', // 金色在印度文化中也很重要
      unlucky: 'dark-500',
    },
    symbols: {
      success: '✓',
      error: '✗',
      warning: '⚠',
      info: 'ℹ',
      luck: '🪔', // 排灯节灯
      spirituality: '🕉', // 奥姆符号
    },
    dateFormat: 'dd/MM/yyyy',
    timeFormat: '12h',
    numberFormat: 'hi-IN',
    currency: 'INR',
    behaviors: {
      showAnimations: true,
      preferredColorScheme: 'light',
      preferredIconStyle: 'symbolic',
      preferredInteractionStyle: 'indirect',
    },
  },

  // 日语
  ja: {
    fontFamily: 'japanese',
    lineHeight: 1.7,
    letterSpacing: '0.05em',
    expansionFactor: 1.1,
    colorPreferences: {
      primary: 'mystical-500',
      secondary: 'gold-500',
      accent: 'mystical-300',
      lucky: 'gold-400',
      unlucky: 'dark-500',
    },
    symbols: {
      success: '✓',
      error: '✗',
      warning: '⚠',
      info: 'ℹ',
      luck: '🎋', // 七夕竹子
      spirituality: '⛩', // 神社
    },
    dateFormat: 'yyyy年MM月dd日',
    timeFormat: '24h',
    numberFormat: 'ja-JP',
    currency: 'JPY',
    behaviors: {
      showAnimations: true,
      preferredColorScheme: 'light',
      preferredIconStyle: 'minimal',
      preferredInteractionStyle: 'indirect',
    },
  },
};

// 文化上下文类型
interface CulturalContextType {
  config: CulturalConfig;
  locale: Locale;
  isLoaded: boolean;
}

// 创建文化上下文
const CulturalContext = createContext<CulturalContextType | undefined>(
  undefined
);

// 文化提供者属性
interface CulturalProviderProps {
  children: React.ReactNode;
  locale?: Locale;
}

// 文化提供者组件
export function CulturalProvider({
  children,
  locale: propLocale,
}: CulturalProviderProps) {
  const currentLocale = useLocale() as Locale;
  const locale = propLocale || currentLocale;
  const [isLoaded, setIsLoaded] = useState(false);

  // 合并默认配置和语言特定配置
  const config = {
    ...defaultCulturalConfig,
    ...(culturalConfigs[locale] || {}),
  } as CulturalConfig;

  useEffect(() => {
    setIsLoaded(true);
  }, []);

  return (
    <CulturalContext.Provider value={{ config, locale, isLoaded }}>
      {children}
    </CulturalContext.Provider>
  );
}

// 使用文化上下文的Hook
export function useCultural() {
  const context = useContext(CulturalContext);
  if (context === undefined) {
    throw new Error('useCultural must be used within a CulturalProvider');
  }
  return context;
}

// 文化敏感的CSS变量提供者
export function CulturalCSSVariables() {
  const { config, isLoaded } = useCultural();

  if (!isLoaded) return null;

  // 将配置转换为CSS变量
  const cssVars = {
    '--font-family': getFontFamilyValue(config.fontFamily),
    '--line-height': config.lineHeight.toString(),
    '--letter-spacing': config.letterSpacing,
    '--word-break': config.wordBreak || 'normal',
    '--expansion-factor': config.expansionFactor.toString(),
    '--primary-color': `var(--${config.colorPreferences.primary})`,
    '--secondary-color': `var(--${config.colorPreferences.secondary})`,
    '--accent-color': `var(--${config.colorPreferences.accent})`,
    '--lucky-color': `var(--${config.colorPreferences.lucky})`,
    '--unlucky-color': `var(--${config.colorPreferences.unlucky})`,
    '--symbol-success': `"${config.symbols.success}"`,
    '--symbol-error': `"${config.symbols.error}"`,
    '--symbol-warning': `"${config.symbols.warning}"`,
    '--symbol-info': `"${config.symbols.info}"`,
    '--symbol-luck': `"${config.symbols.luck}"`,
    '--symbol-spirituality': `"${config.symbols.spirituality}"`,
  };

  return (
    <style jsx global>{`
      :root {
        ${Object.entries(cssVars)
          .map(([key, value]) => `${key}: ${value};`)
          .join('\n')}
      }
    `}</style>
  );
}

// 辅助函数：获取字体系列值
function getFontFamilyValue(fontFamily: string): string {
  switch (fontFamily) {
    case 'chinese':
      return 'var(--font-noto-sans-sc), "Noto Sans SC", "PingFang SC", "Microsoft YaHei", "SimHei", sans-serif';
    case 'japanese':
      return 'var(--font-noto-sans-jp), "Noto Sans JP", "Hiragino Sans", "Yu Gothic", "Meiryo", sans-serif';
    case 'hindi':
      return 'var(--font-noto-sans), "Noto Sans Devanagari", "Mangal", "Kokila", "Arial Unicode MS", sans-serif';
    case 'latin':
    default:
      return 'var(--font-inter), "Inter", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif';
  }
}
